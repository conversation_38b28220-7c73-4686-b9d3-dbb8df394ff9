---
title: Project Manual
---

# **Chatops Manual**

## Introduction

The project aims to provide incident health check through HA Chat for SC3 members.

Currently, the chatops supports sybase, oracle and repserver health check and non read operation (e.g. repserver restart).

## Binary (Linux)

### start_chatops.sh

```bash
./start_chatops.sh [dev|sit|prd] [fg|bg] [java_path]
```

e.g. `start_chatops.sh dev bg /usr/bin/java`

:::info
**fg** means foreground process. **bg** means background process.
:::

### kill_chatops_bg_process.sh

```bash
./kill_chatops_bg_process.sh [dev|sit|prd]
```

### auto_start_chatops.sh

Usage: Script for a crontab job to automatically start ChatOps if it is down.

An email notification will be sent each time the script is triggered.
If the script fails to start ChatOps after three consecutive attempts, it will stop trying.

```bash
./auto_start_chatops.sh [dev|sit|prd] [java_path]
```
e.g. `*/5 * * * * /dbms/maintain/home/<USER>/chatops/auto_start_chatops.sh dev /usr/bin/java`

## Chatops useful link <a name="chatops-useful-link"></a>

| Environment        | URL                                                        |
| ------------------ | :----------------------------------------------------------|
| DEV web portal     | https://hachatwebtesting.server.ha.org.hk/login            |
| SIT web portal     | https://hachatwebsit.server.ha.org.hk/login                |
| PROD web portal    | https://hachatweb.server.ha.org.hk/login                   |
| SC4 chatops manual | https://hagithub.home/pages/EAP/chatops/docs/chatops/cover |

## Access HA Chat (DEV) <a name="ha-chat-dev"></a>

### Web version

1. Access HA Chat (DEV) website https://hachatwebtesting.server.ha.org.hk/chatroom
2. Login with CORPDEV account.
3. Start itbot in the "ChatOps_Dev(SC3)" chatroom

### Mobile app version

1. Access HA Chat (DEV) registration website. Follow the instruction to install the mobile app.
   https://hachatwebtesting.server.ha.org.hk/home/<USER>
2. Start itbot in the "ChatOps_Dev(SC3)" chatroom

## How to start chatops <a name="how-to-start"></a>

Enter `@itbot` to initialize ChatOps.  
Follow the instructions and select the appropriate options.

![Initial Chatops](./assets/init_chatops.png)

## Available Buttons <a name="buttons"></a>

| Database  | Button                   | Usage                                                         |
| --------- | ------------------------ | ------------------------------------------------------------- |
| Sybase    | ASE_IHC                  | Incident health check script                                  |
| Oracle    | CIMS_HEALTH_CHECK        | Health check for CIMS project only                            |
| Oracle    | DB_ENABLE                | Check if db/listeners are enabled in clusterware              |
| Oracle    | HAORA                    | Basic check of all db in the host and clusterware             |
| Oracle    | ORACRS                   | Basic check of clusterware                                    |
| Oracle    | ORAAERRALERT             | Filter ORA errors in alert log                                |
| Oracle    | ORAINFO                  | List IP related information                                   |
| Oracle    | ORAROLE                  | List db up/down and database role                             |
| Oracle    | ORASERVICE               | List all db services in host                                  |
| OMA       | OMA status               | Check Oracle OEM agent status (up/down)                       |
| Repserver | DBSUB_DROP               | Drop rep subscription                                         |
| Repserver | DBSUB_Repdbs_MDROP       | Drop rep def                                                  |
| Repserver | FIND_DBSUB               | Find rep subscription                                         |
| Repserver | FIND_DBSUB_REPDBS        | Find rep def                                                  |
| Repserver | REP_HC                   | Health check through chk_que.sh, chk_down.sh and chk_dbsub.sh |
| Repserver | RS_STARTUP               | Restart repserver in normal mode                              |
| Repserver | RS_STARTUP_STDALONE      | Restart repserver in standalone mode                          |
| Repserver | SQM_PURGE                | Purge stable queue                                            |
| Repserver | SQM_SHOW                 | Show stable queue content                                     |
| Cloudera  | CLOUDERA_API_CHK_ROLE    | Check all instances status (up/down) of a role                |
| Cloudera  | CLOUDERA_API_API_RESTART | Restart an instance of a role                                 |
| Cloudera  | CLOUDERA_API_START       | Start an instance of a role                                   |
| Cloudera  | CLOUDERA_API_STOP        | Stop an instance of a role                                    |

## Available Health Check Module <a name="available-module"></a>

All health check modules implement the ICommand interface.

| Module Name       | Description                                                        |
| ----------------- | ------------------------------------------------------------------ |
| AnsibleService    | Executes playbooks using the Ansible Automation Platform API.      |
| ClouderaService   | Interacts with the Cloudera Manager API for health checks and operations (e.g., start, stop, restart). |
| GranfanaService   | Captures dashboards from SC3 Grafana.                              |
| SshModule         | Executes shell commands via SSH.                                   |

## Output sample <a name="output"></a>

### PDF normal result

The result text box is the stdout of the script command.

![Example banner](./assets/normal.png)

### PDF with script exit with return code > 0

In case of the script exit with return code > 0, the exit code and stderr will be printed in the red text box.

Stdout will still be printed in the white text box.

![Example banner](./assets/error.png)

### Wildcast search result > 30 item

The chatops support free text input for searching hosts/instances/projects.

- When the search result is less than 30 items, the chatops will convert the items to buttons for user to interact with.

  ![Example banner](./assets/list30-.png)

- When the search result is greater than 30 items, the chatops will convert the items to pdf.
  User will need to further search the target in the pdf, and then restart the chatops session.

  ![Example banner](./assets/list30+.png)

### Java program exception

Chatops will reply a message with the exception message.

![Example banner](./assets/java-exception.png)
